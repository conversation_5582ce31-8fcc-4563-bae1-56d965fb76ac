import React from "react";

import Link from "next/link";
import styled from "styled-components";

const NotifySectionContainer = styled.div`
  height: 230px;
  border: 1px solid #e2e3e5;
  border-radius: 12px;
  background-color: #fff;
  padding: 20px;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
`;

const Title = styled.h3`
  font-size: 16px;
  font-weight: 500;
  color: #243544;
  margin: 0;
  font-family: "NotoSansJP";
  line-height: 1;
`;

const ViewAllLink = styled(Link)`
  color: #007aff;
  font-size: 14px;
  text-decoration: none;
  font-family: "NotoSansJP";
  line-height: 1;

  &:hover {
    text-decoration: underline;
  }
`;

const NotificationList = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
`;

const NotificationItem = styled.div`
  height: 54px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 8px 4px;
  border-top: solid 1px #e2e3e5;
  border-bottom: solid 1px #e2e3e5;
`;

const NotificationHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const DateText = styled.span`
  color: #6a757d;
  font-size: 14px;
  font-family: "Roboto";
  line-height: 1;
`;

const Badge = styled.span<{ $type: "important" | "service" | "campaign" }>`
  height: 20px;
  font-size: 12px;
  color: #fff;
  padding: 0 16px;
  border-radius: 2px;
  font-family: "NotoSansJP";
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;

  span {
    line-height: 1;
  }

  ${({ $type }) => {
    switch ($type) {
      case "important":
        return `background-color: #e74c3c;`;
      case "service":
        return `background-color: #27ae60;`;
      case "campaign":
        return `background-color: #f39c12;`;
      default:
        return `background-color: #999;`;
    }
  }}
`;

const NotificationTitle = styled.div`
  color: #243544;
  font-size: 14px;
  line-height: 1;
  font-weight: bold;
`;

// Sample notification data
const notifications = [
  {
    id: 1,
    date: "2025/04/01",
    type: "important" as const,
    typeLabel: "重要",
    title: "システムメンテナンスのお知らせ",
  },
  {
    id: 2,
    date: "2025/03/31",
    type: "campaign" as const,
    typeLabel: "キャンペーン",
    title: "電子カルテ紹介キャンペーンのお知らせ",
  },
  {
    id: 3,
    date: "2025/03/01",
    type: "important" as const,
    typeLabel: "重要",
    title: "システムメンテナンスのお知らせ",
  },
];

export const NotifySection = () => {
  return (
    <NotifySectionContainer>
      <Header>
        <Title>GMOヘルステックからのお知らせ</Title>
        <ViewAllLink href="/notifications">すべて見る</ViewAllLink>
      </Header>
      <NotificationList>
        {notifications.map((notification) => (
          <NotificationItem key={notification.id}>
            <NotificationHeader>
              <DateText>{notification.date}</DateText>
              <Badge $type={notification.type}>
                <span>{notification.typeLabel}</span>
              </Badge>
              {notification.id === 3 && (
                <Badge $type="service">サービス改善</Badge>
              )}
            </NotificationHeader>
            <NotificationTitle>{notification.title}</NotificationTitle>
          </NotificationItem>
        ))}
      </NotificationList>
    </NotifySectionContainer>
  );
};
