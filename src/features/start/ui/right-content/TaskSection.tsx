import React from "react";

import styled from "styled-components";
import Link from "next/link";

// eslint-disable-next-line import/no-restricted-paths
import { TaskTable } from "@/features/task/ui/list/TaskListTable";
import { TaskSortType } from "@/apis/gql/generated/types";

const TaskSectionContainer = styled.div`
  height: 100%;
  border: 1px solid #e2e3e5;
  border-radius: 12px;
  background-color: #fff;
  padding: 20px;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
`;

const Title = styled.h3`
  font-size: 16px;
  font-weight: 500;
  color: #243544;
  margin: 0;
`;

const ViewAllLink = styled(Link)`
  color: #007aff;
  font-size: 14px;
  text-decoration: none;
  font-family: "NotoSansJP", sans-serif;

  &:hover {
    text-decoration: underline;
  }
`;

const TaskTableWrapper = styled.div`
  height: 100%;
`;

export const TaskSection = () => {
  // Sample data
  const sampleTasks = [
    {
      taskId: 1,
      title: "患者Aのフォローアップ",
      category: { id: 1, name: "フォローアップ", hospitalId: 1, order: 1 },
      categoryId: 1,
      createdDate: new Date().toISOString(),
      createdStaff: { staffId: 1, staffName: "山田太郎" },
      responsibleStaff: { staffId: 2, staffName: "佐藤花子" },
      status: {
        id: 1,
        name: "未完了",
        color: "#ff0000",
        hospitalId: 1,
        order: 1,
      },
      statusId: 1,
      expiredAt: new Date(Date.now() + 86400000).toISOString(),
      patient: {
        patientID: 1,
        patientName: "患者A",
        patientNameKana: "カンジャエー",
        gender: 1,
        phoneNumber1: "090-1234-5678",
        birthdate: "1980-01-01",
        lastAppointmentDate: "2024-06-01",
        nextAppointmentDate: "2024-07-01",
        lastAppointmentDepartment: "内科",
        nextAppointmentDepartment: "外科",
      },
      hospitalId: 1,
      isAutoCreated: false,
    },
    {
      taskId: 2,
      title: "患者Bの検査予約",
      category: { id: 2, name: "検査", hospitalId: 1, order: 2 },
      categoryId: 2,
      createdDate: new Date().toISOString(),
      createdStaff: { staffId: 3, staffName: "鈴木一郎" },
      responsibleStaff: { staffId: 4, staffName: "田中美咲" },
      status: {
        id: 2,
        name: "完了",
        color: "#00ff00",
        hospitalId: 1,
        order: 2,
      },
      statusId: 2,
      expiredAt: new Date(Date.now() - 86400000).toISOString(),
      patient: {
        patientID: 2,
        patientName: "患者B",
        patientNameKana: "カンジャビー",
        gender: 2,
        phoneNumber1: "080-9876-5432",
        birthdate: "1975-05-05",
        lastAppointmentDate: "2024-05-20",
        nextAppointmentDate: "2024-06-20",
        lastAppointmentDepartment: "外科",
        nextAppointmentDepartment: "内科",
      },
      hospitalId: 1,
      isAutoCreated: false,
    },
  ];

  return (
    <TaskSectionContainer>
      <Header>
        <Title>タスク</Title>
        <ViewAllLink href="/notifications">すべて見る</ViewAllLink>
      </Header>
      <TaskTableWrapper>
        <TaskTable
          tasks={sampleTasks}
          loading={false}
          isLoadingMore={false}
          sortOrder={undefined}
          sortType={TaskSortType.CreatedDate}
          onSortTasks={() => {}}
          onLoadMore={() => {}}
        />
      </TaskTableWrapper>
    </TaskSectionContainer>
  );
};
