import React from "react";

import styled from "styled-components";

const HospitalBoardContainer = styled.div`
  height: 310px;
  border: 1px solid #e2e3e5;
  border-radius: 12px;
  background-color: #fff;
  padding: 20px;
  display: flex;
  flex-direction: column;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
`;

const Title = styled.h3`
  font-size: 16px;
  font-weight: 500;
  color: #243544;
  margin: 0;
  font-family: "NotoSansJP", sans-serif;
`;

const EditButton = styled.button`
  width: 80px;
  height: 36px;
  border-radius: 24px;
  background-color: #43c3d5;
  color: #fff;
  font-size: 14px;
  border: none;
  cursor: pointer;
  font-family: "NotoSansJP", sans-serif;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #3ab0c1;
  }
`;

const NotificationList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 12px;
`;

const NotificationItem = styled.div`
  color: #243544;
  font-size: 14px;
  line-height: 1.5;
  font-family: "NotoSansJP", sans-serif;
  padding: 8px 0;
`;

export const HospitalBoard = () => {
  const handleEditClick = () => {
    // TODO: Implement edit functionality
    console.log("Edit button clicked");
  };

  return (
    <HospitalBoardContainer>
      <Header>
        <Title>院内掲示板</Title>
        <EditButton onClick={handleEditClick}>編集</EditButton>
      </Header>
      <NotificationList>
        <NotificationItem>4月21日院長学会のため終日不在</NotificationItem>
        <NotificationItem>メジコン錠15mg在庫少、既に発注済み</NotificationItem>
      </NotificationList>
    </HospitalBoardContainer>
  );
};
