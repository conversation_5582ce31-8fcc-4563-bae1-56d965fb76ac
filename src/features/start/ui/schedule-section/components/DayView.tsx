import React from "react";

import styled from "styled-components";

import type { DayViewProps } from "../types";

const DayViewContainer = styled.div`
  flex: 1;
  overflow-y: auto;
  padding: 0;
`;

const TimeSlotContainer = styled.div`
  display: flex;
  min-height: 48px;
`;

const TimeLabel = styled.div`
  width: 32px;
  padding: 2px;
  font-size: 12px;
  color: #6a757d;
  font-family: Roboto;
`;

const EventArea = styled.div`
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: stretch;
  gap: 2px;
  padding: 2px;
  border-left: solid 1px #e2e3e5;
  border-top: solid 1px #e2e3e5;
  border-right: solid 1px #e2e3e5;
`;

const EventBlock = styled.div<{
  $type: "appointment" | "examination" | "break";
}>`
  height: 100%;
  background-color: #d9f3f7;
  padding: 4px;
  font-size: 14px;
  color: #243544;
  white-space: pre-line;
  line-height: 1;
`;

// Sample time slots for demonstration
const timeSlots = [
  "09:00",
  "10:00",
  "11:00",
  "12:00",
  "13:00",
  "14:00",
  "15:00",
  "16:00",
  "17:00",
];

// Sample events for demonstration
const sampleEvents = [
  {
    id: "1",
    title: "搬入",
    startTime: "09:00",
    endTime: "10:00",
    type: "appointment" as const,
  },
  {
    id: "2",
    title: "患者受入",
    startTime: "13:00",
    endTime: "14:00",
    type: "examination" as const,
  },
  {
    id: "3",
    title: "検査準備",
    startTime: "13:00",
    endTime: "14:00",
    type: "examination" as const,
  },
];

export const DayView: React.FC<DayViewProps> = ({ events = sampleEvents }) => {
  const getEventsForTimeSlot = (timeSlot: string) => {
    return events.filter((event) => event.startTime === timeSlot);
  };

  return (
    <DayViewContainer>
      {timeSlots.map((timeSlot) => {
        const slotEvents = getEventsForTimeSlot(timeSlot);

        return (
          <TimeSlotContainer key={timeSlot}>
            <TimeLabel>{timeSlot}</TimeLabel>
            <EventArea>
              {slotEvents.map((event) => (
                <EventBlock key={event.id} $type={event.type}>
                  {event.title}
                </EventBlock>
              ))}
            </EventArea>
          </TimeSlotContainer>
        );
      })}
    </DayViewContainer>
  );
};
