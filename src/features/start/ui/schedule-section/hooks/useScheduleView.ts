import { useState, useCallback } from "react";

import dayjs from "dayjs";

import type { ViewType } from "../types";
import type { Dayjs } from "dayjs";

export const useScheduleView = () => {
  const [currentView, setCurrentView] = useState<ViewType>("month");
  const [currentDate, setCurrentDate] = useState<Dayjs>(dayjs());

  const navigateDate = useCallback(
    (direction: "prev" | "next") => {
      setCurrentDate((prevDate) => {
        switch (currentView) {
          case "day":
            return direction === "prev"
              ? prevDate.subtract(1, "day")
              : prevDate.add(1, "day");
          case "week":
            return direction === "prev"
              ? prevDate.subtract(1, "week")
              : prevDate.add(1, "week");
          case "month":
            return direction === "prev"
              ? prevDate.subtract(1, "month")
              : prevDate.add(1, "month");
          case "agenda":
            return direction === "prev"
              ? prevDate.subtract(1, "month")
              : prevDate.add(1, "month");
          default:
            return prevDate;
        }
      });
    },
    [currentView],
  );

  const handleDateChange = useCallback((date: Date) => {
    setCurrentDate(dayjs(date));
  }, []);

  return {
    currentView,
    currentDate,
    setCurrentView,
    navigateDate,
    setCurrentDate,
    handleDateChange,
  };
};
