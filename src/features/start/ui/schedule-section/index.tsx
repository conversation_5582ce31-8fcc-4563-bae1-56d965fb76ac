import React from "react";

import styled from "styled-components";

import { ScheduleHeader, BigCalendarView } from "./components";
import { useScheduleView } from "./hooks";

const ScheduleSectionContainer = styled.div`
  height: 380px;
  border-radius: 12px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  padding: 20px;
`;

export const ScheduleSection = () => {
  const {
    currentView,
    currentDate,
    setCurrentView,
    navigateDate,
    handleDateChange,
  } = useScheduleView();

  const renderCalendarView = () => {
    // Use BigCalendarView for both day and month views
    return (
      <BigCalendarView
        currentDate={currentDate}
        currentView={currentView}
        onNavigate={handleDateChange}
        onView={setCurrentView}
      />
    );
  };

  return (
    <ScheduleSectionContainer>
      <ScheduleHeader
        currentView={currentView}
        currentDate={currentDate}
        onViewChange={setCurrentView}
        onNavigateDate={navigateDate}
      />
      {renderCalendarView()}
    </ScheduleSectionContainer>
  );
};
